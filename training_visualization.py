import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from datetime import datetime
import matplotlib.dates as mdates

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.dpi'] = 100
plt.rcParams['savefig.dpi'] = 300

# 解析训练数据
training_data = """2025-07-24 10:30:05,548 - INFO - Epoch 1/50 | Loss: 0.6873 | Val AUC: 0.7842
2025-07-24 10:30:05,981 - INFO - Epoch 2/50 | Loss: 0.4512 | Val AUC: 0.8315
2025-07-24 10:30:06,412 - INFO - Epoch 3/50 | Loss: 0.3129 | Val AUC: 0.8560
2025-07-24 10:30:06,845 - INFO - Epoch 4/50 | Loss: 0.2458 | Val AUC: 0.8711
2025-07-24 10:30:07,277 - INFO - Epoch 5/50 | Loss: 0.1983 | Val AUC: 0.8804
2025-07-24 10:30:07,710 - INFO - Epoch 6/50 | Loss: 0.1651 | Val AUC: 0.8893
2025-07-24 10:30:08,141 - INFO - Epoch 7/50 | Loss: 0.1420 | Val AUC: 0.8952
2025-07-24 10:30:08,573 - INFO - Epoch 8/50 | Loss: 0.1259 | Val AUC: 0.9017
2025-07-24 10:30:09,005 - INFO - Epoch 9/50 | Loss: 0.1134 | Val AUC: 0.9068
2025-07-24 10:30:09,438 - INFO - Epoch 10/50 | Loss: 0.1042 | Val AUC: 0.9105
2025-07-24 10:30:09,871 - INFO - Epoch 11/50 | Loss: 0.0968 | Val AUC: 0.9134
2025-07-24 10:30:10,302 - INFO - Epoch 12/50 | Loss: 0.0915 | Val AUC: 0.9158
2025-07-24 10:30:10,735 - INFO - Epoch 13/50 | Loss: 0.0864 | Val AUC: 0.9177
2025-07-24 10:30:11,168 - INFO - Epoch 14/50 | Loss: 0.0821 | Val AUC: 0.9193
2025-07-24 10:30:11,600 - INFO - Epoch 15/50 | Loss: 0.0789 | Val AUC: 0.9206
2025-07-24 10:30:12,031 - INFO - Epoch 16/50 | Loss: 0.0754 | Val AUC: 0.9221
2025-07-24 10:30:12,464 - INFO - Epoch 17/50 | Loss: 0.0728 | Val AUC: 0.9230
2025-07-24 10:30:12,896 - INFO - Epoch 18/50 | Loss: 0.0701 | Val AUC: 0.9238
2025-07-24 10:30:13,329 - INFO - Epoch 19/50 | Loss: 0.0683 | Val AUC: 0.9245
2025-07-24 10:30:13,761 - INFO - Epoch 20/50 | Loss: 0.0665 | Val AUC: 0.9251
2025-07-24 10:30:14,194 - INFO - Epoch 21/50 | Loss: 0.0649 | Val AUC: 0.9255
2025-07-24 10:30:14,625 - INFO - Epoch 22/50 | Loss: 0.0638 | Val AUC: 0.9259
2025-07-24 10:30:15,058 - INFO - Epoch 23/50 | Loss: 0.0625 | Val AUC: 0.9263
2025-07-24 10:30:15,491 - INFO - Epoch 24/50 | Loss: 0.0617 | Val AUC: 0.9265
2025-07-24 10:30:15,922 - INFO - Epoch 25/50 | Loss: 0.0609 | Val AUC: 0.9268
2025-07-24 10:30:16,355 - INFO - Epoch 26/50 | Loss: 0.0601 | Val AUC: 0.9270
2025-07-24 10:30:16,787 - INFO - Epoch 27/50 | Loss: 0.0594 | Val AUC: 0.9271
2025-07-24 10:30:17,219 - INFO - Epoch 28/50 | Loss: 0.0588 | Val AUC: 0.9269
2025-07-24 10:30:17,651 - INFO - Epoch 29/50 | Loss: 0.0581 | Val AUC: 0.9272
2025-07-24 10:30:18,084 - INFO - Epoch 30/50 | Loss: 0.0575 | Val AUC: 0.9273
2025-07-24 10:30:18,517 - INFO - Epoch 31/50 | Loss: 0.0570 | Val AUC: 0.9271
2025-07-24 10:30:18,948 - INFO - Epoch 32/50 | Loss: 0.0566 | Val AUC: 0.9268
2025-07-24 10:30:19,381 - INFO - Epoch 33/50 | Loss: 0.0561 | Val AUC: 0.9270
2025-07-24 10:30:19,813 - INFO - Epoch 34/50 | Loss: 0.0558 | Val AUC: 0.9267
2025-07-24 10:30:20,246 - INFO - Epoch 35/50 | Loss: 0.0553 | Val AUC: 0.9266
2025-07-24 10:30:20,678 - INFO - Epoch 36/50 | Loss: 0.0550 | Val AUC: 0.9264
2025-07-24 10:30:21,110 - INFO - Epoch 37/50 | Loss: 0.0546 | Val AUC: 0.9265
2025-07-24 10:30:21,542 - INFO - Epoch 38/50 | Loss: 0.0544 | Val AUC: 0.9262
2025-07-24 10:30:21,975 - INFO - Epoch 39/50 | Loss: 0.0541 | Val AUC: 0.9260
2025-07-24 10:30:22,408 - INFO - Epoch 40/50 | Loss: 0.0538 | Val AUC: 0.9258
2025-07-24 10:30:22,840 - INFO - Epoch 41/50 | Loss: 0.0536 | Val AUC: 0.9259
2025-07-24 10:30:23,272 - INFO - Epoch 42/50 | Loss: 0.0534 | Val AUC: 0.9257
2025-07-24 10:30:23,704 - INFO - Epoch 43/50 | Loss: 0.0531 | Val AUC: 0.9255
2025-07-24 10:30:24,136 - INFO - Epoch 44/50 | Loss: 0.0529 | Val AUC: 0.9256
2025-07-24 10:30:24,569 - INFO - Epoch 45/50 | Loss: 0.0528 | Val AUC: 0.9254
2025-07-24 10:30:25,002 - INFO - Epoch 46/50 | Loss: 0.0526 | Val AUC: 0.9253
2025-07-24 10:30:25,434 - INFO - Epoch 47/50 | Loss: 0.0525 | Val AUC: 0.9251
2025-07-24 10:30:25,865 - INFO - Epoch 48/50 | Loss: 0.0522 | Val AUC: 0.9249
2025-07-24 10:30:26,298 - INFO - Epoch 49/50 | Loss: 0.0522 | Val AUC: 0.9248
2025-07-24 10:30:26,730 - INFO - Epoch 50/50 | Loss: 0.0521 | Val AUC: 0.9246"""

# 推荐系统数据
recommendation_data = {
    'knowledge_ids': [43, 58, 104, 119, 205, 215, 230, 293, 488, 544],
    'scores': [0.9985, 0.9971, 0.9962, 0.9958, 0.9940, 0.9931, 0.9925, 0.9917, 0.9904, 0.9899],
    'ranks': list(range(1, 11))
}

def parse_training_log(log_data):
    """解析训练日志数据"""
    lines = log_data.strip().split('\n')
    epochs = []
    losses = []
    val_aucs = []
    timestamps = []
    
    for line in lines:
        parts = line.split(' | ')
        # 提取时间戳
        timestamp_str = line.split(' - ')[0]
        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
        timestamps.append(timestamp)
        
        # 提取epoch
        epoch_part = parts[0].split('Epoch ')[1]
        epoch = int(epoch_part.split('/')[0])
        epochs.append(epoch)
        
        # 提取loss
        loss = float(parts[1].split('Loss: ')[1])
        losses.append(loss)
        
        # 提取val AUC
        val_auc = float(parts[2].split('Val AUC: ')[1])
        val_aucs.append(val_auc)
    
    return pd.DataFrame({
        'epoch': epochs,
        'loss': losses,
        'val_auc': val_aucs,
        'timestamp': timestamps
    })

def create_training_visualizations():
    """创建训练过程可视化图表"""
    df = parse_training_log(training_data)
    
    # 创建大图表，调整布局参数
    fig = plt.figure(figsize=(24, 18))
    
    # 1. 训练损失和验证AUC随epoch变化 (双y轴)
    ax1 = plt.subplot(2, 3, 1)
    ax2 = ax1.twinx()
    
    line1 = ax1.plot(df['epoch'], df['loss'], 'b-', linewidth=3, label='训练损失', marker='o', markersize=5)
    line2 = ax2.plot(df['epoch'], df['val_auc'], 'r-', linewidth=3, label='验证AUC', marker='s', markersize=5)
    
    ax1.set_xlabel('训练轮次 (Epoch)', fontsize=12)
    ax1.set_ylabel('训练损失', color='blue', fontsize=12)
    ax2.set_ylabel('验证AUC', color='red', fontsize=12)
    ax1.set_title('训练过程：损失与AUC变化', fontsize=16, fontweight='bold', pad=20)
    ax1.grid(True, alpha=0.3)
    
    # 标注最佳AUC点 - 调整位置和样式
    best_auc_idx = df['val_auc'].idxmax()
    best_epoch = df.loc[best_auc_idx, 'epoch']
    best_auc = df.loc[best_auc_idx, 'val_auc']
    ax2.annotate(f'最佳AUC: {best_auc:.4f}\n第{best_epoch}轮', 
                xy=(best_epoch, best_auc), xytext=(best_epoch-8, best_auc+0.01),
                arrowprops=dict(arrowstyle='->', color='red', lw=2),
                fontsize=10, ha='center',
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', 
                         edgecolor='red', alpha=0.8))
    
    # 图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='center right', fontsize=11)
    
    # 2. 训练时间分析
    ax3 = plt.subplot(2, 3, 2)
    time_diff = [(t - df['timestamp'].iloc[0]).total_seconds() for t in df['timestamp']]
    ax3.plot(df['epoch'], time_diff, 'g-', linewidth=3, marker='o', markersize=5)
    ax3.set_xlabel('训练轮次 (Epoch)', fontsize=12)
    ax3.set_ylabel('累计训练时间 (秒)', fontsize=12)
    ax3.set_title('训练时间进度', fontsize=16, fontweight='bold', pad=20)
    ax3.grid(True, alpha=0.3)
    
    # 添加总训练时间标注
    total_time = time_diff[-1]
    ax3.text(0.05, 0.95, f'总训练时间: {total_time:.1f}秒\n平均每轮: {total_time/50:.2f}秒', 
             transform=ax3.transAxes, verticalalignment='top', fontsize=11,
             bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgreen', alpha=0.8))
    
    # 3. 损失收敛分析
    ax4 = plt.subplot(2, 3, 3)
    # 计算损失的移动平均
    window_size = 5
    df['loss_ma'] = df['loss'].rolling(window=window_size).mean()
    
    ax4.plot(df['epoch'], df['loss'], 'lightblue', alpha=0.6, label='原始损失', linewidth=2)
    ax4.plot(df['epoch'], df['loss_ma'], 'darkblue', linewidth=3, label=f'{window_size}轮移动平均')
    ax4.set_xlabel('训练轮次 (Epoch)', fontsize=12)
    ax4.set_ylabel('训练损失', fontsize=12)
    ax4.set_title('损失收敛趋势分析', fontsize=16, fontweight='bold', pad=20)
    ax4.legend(fontsize=11)
    ax4.grid(True, alpha=0.3)
    ax4.set_yscale('log')  # 对数刻度更好地显示损失下降
    
    # 4. AUC改进分析
    ax5 = plt.subplot(2, 3, 4)
    auc_improvement = df['val_auc'].diff()
    colors = ['red' if x < 0 else 'green' for x in auc_improvement[1:]]
    
    bars = ax5.bar(df['epoch'][1:], auc_improvement[1:], color=colors, alpha=0.7, width=0.8)
    ax5.axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=2)
    ax5.set_xlabel('训练轮次 (Epoch)', fontsize=12)
    ax5.set_ylabel('AUC变化量', fontsize=12)
    ax5.set_title('AUC改进/下降分析', fontsize=16, fontweight='bold', pad=20)
    ax5.grid(True, alpha=0.3, axis='y')
    
    # 5. 推荐系统结果可视化
    ax6 = plt.subplot(2, 3, 5)
    colors = plt.cm.viridis(np.linspace(0, 1, len(recommendation_data['knowledge_ids'])))
    bars = ax6.barh(range(len(recommendation_data['knowledge_ids'])), 
                    recommendation_data['scores'], color=colors, height=0.7)
    
    ax6.set_yticks(range(len(recommendation_data['knowledge_ids'])))
    ax6.set_yticklabels([f"知识点 {kid}" for kid in recommendation_data['knowledge_ids']], fontsize=10)
    ax6.set_xlabel('推荐得分', fontsize=12)
    ax6.set_title('Top 10 推荐知识点得分', fontsize=16, fontweight='bold', pad=20)
    ax6.grid(True, alpha=0.3, axis='x')
    
    # 添加数值标签
    for i, (bar, score) in enumerate(zip(bars, recommendation_data['scores'])):
        ax6.text(score + 0.0002, i, f'{score:.4f}', 
                va='center', ha='left', fontsize=10)
    
    # 6. 模型性能总结 - 优化布局和大小
    ax7 = plt.subplot(2, 3, 6)
    ax7.axis('off')
    
    # 计算关键指标
    final_loss = df['loss'].iloc[-1]
    final_auc = df['val_auc'].iloc[-1]
    max_auc = df['val_auc'].max()
    loss_reduction = (df['loss'].iloc[0] - final_loss) / df['loss'].iloc[0] * 100
    
    # 简化总结内容，减小文本框大小
    summary_text = f"""模型训练总结

训练配置:
• 总轮次: 50 轮
• 最佳轮次: 第 {best_epoch} 轮

关键指标:
• 损失降幅: {loss_reduction:.1f}%
• 最佳AUC: {max_auc:.4f}
• 训练时间: {total_time:.1f}秒

推荐系统:
• 最高得分: {max(recommendation_data['scores']):.4f}
• 最低得分: {min(recommendation_data['scores']):.4f}"""
    
    ax7.text(0.1, 0.9, summary_text, transform=ax7.transAxes, 
             verticalalignment='top', fontsize=11,
             bbox=dict(boxstyle='round,pad=0.8', facecolor='lightblue', 
                      alpha=0.7, edgecolor='navy'))
    
    # 调整子图间距
    plt.subplots_adjust(left=0.08, bottom=0.08, right=0.92, top=0.92, 
                        wspace=0.35, hspace=0.35)
    plt.savefig('training_comprehensive_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    return df

def create_recommendation_analysis():
    """创建推荐系统详细分析图表"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 14))
    
    # 1. 推荐得分分布
    scores = recommendation_data['scores']
    ax1.hist(scores, bins=8, alpha=0.7, color='skyblue', edgecolor='black', linewidth=1.5)
    ax1.axvline(np.mean(scores), color='red', linestyle='--', linewidth=3, 
               label=f'平均值: {np.mean(scores):.4f}')
    ax1.set_xlabel('推荐得分', fontsize=12)
    ax1.set_ylabel('频数', fontsize=12)
    ax1.set_title('推荐得分分布', fontsize=16, fontweight='bold', pad=20)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # 2. 知识点ID vs 得分散点图
    colors = plt.cm.plasma(np.linspace(0, 1, len(recommendation_data['knowledge_ids'])))
    scatter = ax2.scatter(recommendation_data['knowledge_ids'], scores, 
                         c=colors, s=150, alpha=0.8, edgecolors='black', linewidth=1.5)
    ax2.set_xlabel('知识点ID', fontsize=12)
    ax2.set_ylabel('推荐得分', fontsize=12)
    ax2.set_title('知识点ID与推荐得分关系', fontsize=16, fontweight='bold', pad=20)
    ax2.grid(True, alpha=0.3)
    
    # 添加趋势线
    z = np.polyfit(recommendation_data['knowledge_ids'], scores, 1)
    p = np.poly1d(z)
    ax2.plot(recommendation_data['knowledge_ids'], p(recommendation_data['knowledge_ids']), 
             "r--", alpha=0.8, linewidth=3, label=f'趋势线 (斜率: {z[0]:.6f})')
    ax2.legend(fontsize=12)
    
    # 3. 得分差异分析
    score_diffs = [scores[i] - scores[i+1] for i in range(len(scores)-1)]
    ax3.bar(range(1, len(score_diffs)+1), score_diffs, alpha=0.7, color='orange', 
           edgecolor='black', linewidth=1)
    ax3.set_xlabel('排名间隔', fontsize=12)
    ax3.set_ylabel('得分差异', fontsize=12)
    ax3.set_title('相邻排名得分差异', fontsize=16, fontweight='bold', pad=20)
    ax3.grid(True, alpha=0.3)
    
    # 4. 推荐质量指标
    ax4.axis('off')
    
    # 计算推荐质量指标
    score_range = max(scores) - min(scores)
    score_std = np.std(scores)
    score_mean = np.mean(scores)
    
    quality_text = f"""推荐系统质量分析

得分统计:
   • 最高得分: {max(scores):.4f}
   • 最低得分: {min(scores):.4f}
   • 得分范围: {score_range:.4f}
   • 平均得分: {score_mean:.4f}
   • 标准差: {score_std:.4f}

推荐质量:
   • 得分区间: [{min(scores):.3f}, {max(scores):.3f}]
   • 相对变异系数: {(score_std/score_mean)*100:.2f}%
   • Top3平均分: {np.mean(scores[:3]):.4f}
   • 最大得分差: {max(score_diffs):.4f}

推荐分布:
   • 高分区 (>0.995): {sum(1 for s in scores if s > 0.995)} 个
   • 中分区 (0.990-0.995): {sum(1 for s in scores if 0.990 <= s <= 0.995)} 个
   • 低分区 (<0.990): {sum(1 for s in scores if s < 0.990)} 个"""
    
    ax4.text(0.05, 0.95, quality_text, transform=ax4.transAxes,
             verticalalignment='top', fontsize=12,
             bbox=dict(boxstyle='round,pad=1', facecolor='lightyellow', alpha=0.8))
    
    # 调整子图间距
    plt.subplots_adjust(left=0.08, bottom=0.08, right=0.92, top=0.92, 
                        wspace=0.25, hspace=0.3)
    plt.savefig('recommendation_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_performance_heatmap():
    """创建性能热力图"""
    df = parse_training_log(training_data)
    
    # 创建性能矩阵 (每10轮为一组)
    epochs_grouped = df.groupby(df['epoch'] // 10).agg({
        'loss': ['mean', 'min', 'max'],
        'val_auc': ['mean', 'min', 'max']
    }).round(4)
    
    # 展平列名
    epochs_grouped.columns = ['_'.join(col).strip() for col in epochs_grouped.columns]
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
    
    # 损失热力图
    loss_data = epochs_grouped[['loss_mean', 'loss_min', 'loss_max']].values
    im1 = ax1.imshow(loss_data.T, cmap='Reds', aspect='auto')
    ax1.set_title('训练损失热力图 (按10轮分组)', fontsize=16, fontweight='bold', pad=20)
    ax1.set_xlabel('轮次组 (×10)', fontsize=12)
    ax1.set_yticks(range(3))
    ax1.set_yticklabels(['平均值', '最小值', '最大值'], fontsize=12)
    ax1.set_xticks(range(len(epochs_grouped)))
    ax1.set_xticklabels([f'{i*10+1}-{(i+1)*10}' for i in range(len(epochs_grouped))], 
                       fontsize=11)
    
    # 添加数值标签
    for i in range(loss_data.T.shape[0]):
        for j in range(loss_data.T.shape[1]):
            ax1.text(j, i, f'{loss_data.T[i, j]:.4f}', 
                    ha='center', va='center', fontsize=10, fontweight='bold')
    
    cbar1 = plt.colorbar(im1, ax=ax1, shrink=0.8)
    cbar1.set_label('损失值', fontsize=12)
    
    # AUC热力图
    auc_data = epochs_grouped[['val_auc_mean', 'val_auc_min', 'val_auc_max']].values
    im2 = ax2.imshow(auc_data.T, cmap='Blues', aspect='auto')
    ax2.set_title('验证AUC热力图 (按10轮分组)', fontsize=16, fontweight='bold', pad=20)
    ax2.set_xlabel('轮次组 (×10)', fontsize=12)
    ax2.set_yticks(range(3))
    ax2.set_yticklabels(['平均值', '最小值', '最大值'], fontsize=12)
    ax2.set_xticks(range(len(epochs_grouped)))
    ax2.set_xticklabels([f'{i*10+1}-{(i+1)*10}' for i in range(len(epochs_grouped))], 
                       fontsize=11)
    
    # 添加数值标签
    for i in range(auc_data.T.shape[0]):
        for j in range(auc_data.T.shape[1]):
            ax2.text(j, i, f'{auc_data.T[i, j]:.4f}', 
                    ha='center', va='center', fontsize=10, fontweight='bold')
    
    cbar2 = plt.colorbar(im2, ax=ax2, shrink=0.8)
    cbar2.set_label('AUC值', fontsize=12)
    
    # 调整子图间距
    plt.subplots_adjust(left=0.08, bottom=0.12, right=0.92, top=0.88, wspace=0.3)
    plt.savefig('performance_heatmap.png', dpi=300, bbox_inches='tight')
    plt.show()

if __name__ == "__main__":
    print("正在生成训练数据可视化图表...")
    
    # 生成综合训练分析图表
    print("1. 生成综合训练分析图表...")
    df = create_training_visualizations()
    
    # 生成推荐系统分析图表
    print("2. 生成推荐系统分析图表...")
    create_recommendation_analysis()
    
    # 生成性能热力图
    print("3. 生成性能热力图...")
    create_performance_heatmap()
    
    print("\n✅ 所有图表生成完成！")
    print("生成的文件:")
    print("- training_comprehensive_analysis.png: 综合训练分析")
    print("- recommendation_analysis.png: 推荐系统分析") 
    print("- performance_heatmap.png: 性能热力图") 