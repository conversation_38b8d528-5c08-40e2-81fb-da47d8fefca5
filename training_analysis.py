import matplotlib.pyplot as plt
import pandas as pd
import re
from datetime import datetime
import numpy as np

def parse_training_log(file_path):
    """解析训练日志文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取训练数据
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ - INFO - Epoch (\d+)/(\d+) \| Loss: ([\d.]+) \| Val AUC: ([\d.]+)'
    matches = re.findall(pattern, content)
    
    data = []
    for match in matches:
        timestamp, epoch, total_epochs, loss, val_auc = match
        data.append({
            'timestamp': datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S'),
            'epoch': int(epoch),
            'total_epochs': int(total_epochs),
            'loss': float(loss),
            'val_auc': float(val_auc)
        })
    
    return pd.DataFrame(data)

def create_visualizations(df):
    """创建可视化图表"""
    # 分离两次训练（根据时间戳判断）
    time_diff = df['timestamp'].diff()
    training_sessions = []
    current_session = []
    
    for i, row in df.iterrows():
        if i == 0 or time_diff.iloc[i].total_seconds() < 3600:  # 1小时内认为是同一次训练
            current_session.append(row)
        else:
            if current_session:
                training_sessions.append(pd.DataFrame(current_session))
                current_session = [row]
    
    if current_session:
        training_sessions.append(pd.DataFrame(current_session))
    
    # 设置matplotlib参数，解决中文显示问题
    plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['figure.dpi'] = 100
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    fig.suptitle('Training Log Analysis', fontsize=16, fontweight='bold')
    
    colors = ['#2E86C1', '#E74C3C', '#28B463', '#F39C12']
    
    # 图1: 损失函数变化
    ax1 = axes[0, 0]
    for i, session in enumerate(training_sessions):
        session_reset = session.reset_index(drop=True)
        ax1.plot(session_reset['epoch'], session_reset['loss'], 
                marker='o', linewidth=2.5, markersize=5, 
                label=f'Training {i+1}', color=colors[i], alpha=0.8)
    ax1.set_xlabel('Epoch', fontsize=12)
    ax1.set_ylabel('Loss', fontsize=12)
    ax1.set_title('Loss Function Convergence', fontsize=14, fontweight='bold')
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')
    
    # 图2: 验证AUC变化
    ax2 = axes[0, 1]
    for i, session in enumerate(training_sessions):
        session_reset = session.reset_index(drop=True)
        ax2.plot(session_reset['epoch'], session_reset['val_auc'], 
                marker='s', linewidth=2.5, markersize=5,
                label=f'Training {i+1}', color=colors[i], alpha=0.8)
    ax2.set_xlabel('Epoch', fontsize=12)
    ax2.set_ylabel('Validation AUC', fontsize=12)
    ax2.set_title('Validation AUC Progress', fontsize=14, fontweight='bold')
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim([0.5, 1.0])
    
    # 图3: 训练效果对比（箱线图）
    ax3 = axes[1, 0]
    if len(training_sessions) >= 2:
        auc_data = []
        loss_data = []
        labels = []
        
        for i, session in enumerate(training_sessions):
            auc_data.append(session['val_auc'].values)
            loss_data.append(session['loss'].values)
            labels.append(f'Training {i+1}')
        
        # 创建双y轴
        ax3_twin = ax3.twinx()
        
        # AUC箱线图
        bp1 = ax3.boxplot(auc_data, positions=[1, 3], widths=0.6, 
                         patch_artist=True, labels=labels)
        for patch, color in zip(bp1['boxes'], colors[:len(training_sessions)]):
            patch.set_facecolor(color)
            patch.set_alpha(0.7)
        
        # Loss箱线图
        bp2 = ax3_twin.boxplot(loss_data, positions=[1.5, 3.5], widths=0.6, 
                              patch_artist=True, labels=[f'Loss {i+1}' for i in range(len(training_sessions))])
        for patch, color in zip(bp2['boxes'], colors[:len(training_sessions)]):
            patch.set_facecolor(color)
            patch.set_alpha(0.4)
        
        ax3.set_ylabel('Validation AUC', color=colors[0], fontsize=12)
        ax3_twin.set_ylabel('Loss', color=colors[1], fontsize=12)
        ax3.set_title('Training Performance Distribution', fontsize=14, fontweight='bold')
        ax3.set_xticks([1.25, 3.25])
        ax3.set_xticklabels(['Training 1', 'Training 2'])
    
    # 图4: 学习曲线平滑对比
    ax4 = axes[1, 1]
    for i, session in enumerate(training_sessions):
        session_reset = session.reset_index(drop=True)
        # 计算平滑的移动平均
        window_size = min(5, len(session_reset))
        smooth_auc = session_reset['val_auc'].rolling(window=window_size, center=True).mean()
        smooth_loss = session_reset['loss'].rolling(window=window_size, center=True).mean()
        
        ax4.plot(session_reset['epoch'], smooth_auc, 
                linewidth=3, label=f'AUC - Training {i+1}', 
                color=colors[i], alpha=0.8)
    
    ax4.set_xlabel('Epoch', fontsize=12)
    ax4.set_ylabel('Smoothed Validation AUC', fontsize=12)
    ax4.set_title('Smoothed Learning Curves', fontsize=14, fontweight='bold')
    ax4.legend(fontsize=11)
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim([0.5, 1.0])
    
    plt.tight_layout()
    plt.savefig('training_analysis.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.show()
    
    return training_sessions

def print_statistics(training_sessions):
    """打印训练统计信息"""
    print("=" * 70)
    print("                    TRAINING STATISTICS ANALYSIS")
    print("=" * 70)
    
    for i, session in enumerate(training_sessions):
        print(f"\nTraining Session {i+1}:")
        print(f"  Epochs Completed: {len(session)}")
        print(f"  Initial Loss: {session['loss'].iloc[0]:.4f}")
        print(f"  Final Loss: {session['loss'].iloc[-1]:.4f}")
        print(f"  Loss Reduction: {session['loss'].iloc[0] - session['loss'].iloc[-1]:.4f}")
        print(f"  Initial AUC: {session['val_auc'].iloc[0]:.4f}")
        print(f"  Final AUC: {session['val_auc'].iloc[-1]:.4f}")
        print(f"  AUC Improvement: {session['val_auc'].iloc[-1] - session['val_auc'].iloc[0]:.4f}")
        
        # 计算训练时间
        total_time = (session['timestamp'].iloc[-1] - session['timestamp'].iloc[0]).total_seconds()
        print(f"  Total Training Time: {total_time:.1f} seconds")
        print(f"  Average Time per Epoch: {total_time/len(session):.2f} seconds")
        
        # 计算稳定性指标
        auc_std = session['val_auc'].std()
        print(f"  AUC Stability (std): {auc_std:.4f}")
    
    if len(training_sessions) >= 2:
        print(f"\nCOMPARATIVE ANALYSIS:")
        auc_improvement = training_sessions[1]['val_auc'].iloc[-1] - training_sessions[0]['val_auc'].iloc[-1]
        loss_improvement = training_sessions[0]['loss'].iloc[-1] - training_sessions[1]['loss'].iloc[-1]
        print(f"  AUC Improvement (Training 2 vs 1): {auc_improvement:.4f}")
        print(f"  Loss Reduction (Training 2 vs 1): {loss_improvement:.4f}")
        
        # 效率分析
        eff1 = (training_sessions[0]['val_auc'].iloc[-1] - training_sessions[0]['val_auc'].iloc[0]) / len(training_sessions[0])
        eff2 = (training_sessions[1]['val_auc'].iloc[-1] - training_sessions[1]['val_auc'].iloc[0]) / len(training_sessions[1])
        print(f"  Training Efficiency (AUC gain per epoch):")
        print(f"    Training 1: {eff1:.6f}")
        print(f"    Training 2: {eff2:.6f}")

def create_summary_report(training_sessions):
    """创建训练总结报告"""
    report = []
    report.append("TRAINING SUMMARY REPORT")
    report.append("=" * 50)
    
    for i, session in enumerate(training_sessions):
        report.append(f"\nTraining {i+1} Summary:")
        report.append(f"- Best AUC: {session['val_auc'].max():.4f}")
        report.append(f"- Lowest Loss: {session['loss'].min():.4f}")
        report.append(f"- Convergence Rate: {'Fast' if len(session) < 45 else 'Moderate'}")
        
        # 判断训练质量
        final_auc = session['val_auc'].iloc[-1]
        if final_auc > 0.9:
            quality = "Excellent"
        elif final_auc > 0.8:
            quality = "Good"
        elif final_auc > 0.7:
            quality = "Fair"
        else:
            quality = "Poor"
        report.append(f"- Training Quality: {quality}")
    
    # 保存报告
    with open('training_report.txt', 'w') as f:
        f.write('\n'.join(report))
    
    return report

if __name__ == "__main__":
    # 解析日志文件
    df = parse_training_log('training.log')
    
    if df.empty:
        print("No training data found in training.log")
        exit(1)
    
    # 创建可视化图表
    training_sessions = create_visualizations(df)
    
    # 打印统计信息
    print_statistics(training_sessions)
    
    # 创建总结报告
    report = create_summary_report(training_sessions)
    
    print(f"\nVisualization saved as 'training_analysis.png'")
    print(f"Summary report saved as 'training_report.txt'")
    print(f"Total training records analyzed: {len(df)}") 