import matplotlib.pyplot as plt
import pandas as pd
import re
from datetime import datetime
import numpy as np

def parse_training_log(file_path):
    """解析训练日志文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 使用正则表达式提取训练数据
    pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}),\d+ - INFO - Epoch (\d+)/(\d+) \| Loss: ([\d.]+) \| Val AUC: ([\d.]+)'
    matches = re.findall(pattern, content)
    
    data = []
    for match in matches:
        timestamp, epoch, total_epochs, loss, val_auc = match
        data.append({
            'timestamp': datetime.strptime(timestamp, '%Y-%m-%d %H:%M:%S'),
            'epoch': int(epoch),
            'total_epochs': int(total_epochs),
            'loss': float(loss),
            'val_auc': float(val_auc)
        })
    
    return pd.DataFrame(data)

def create_visualizations(df):
    """创建中文可视化图表"""
    # 分离两次训练（根据时间戳判断）
    time_diff = df['timestamp'].diff()
    training_sessions = []
    current_session = []
    
    for i, row in df.iterrows():
        if i == 0 or time_diff.iloc[i].total_seconds() < 3600:  # 1小时内认为是同一次训练
            current_session.append(row)
        else:
            if current_session:
                training_sessions.append(pd.DataFrame(current_session))
                current_session = [row]
    
    if current_session:
        training_sessions.append(pd.DataFrame(current_session))
    
    # 设置matplotlib参数，支持中文显示
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['figure.dpi'] = 100
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 10))
    fig.suptitle('训练日志分析报告', fontsize=18, fontweight='bold', y=0.95)
    
    colors = ['#2E86C1', '#E74C3C', '#28B463', '#F39C12']
    
    # 图1: 损失函数变化
    ax1 = axes[0, 0]
    for i, session in enumerate(training_sessions):
        session_reset = session.reset_index(drop=True)
        ax1.plot(session_reset['epoch'], session_reset['loss'], 
                marker='o', linewidth=2.5, markersize=5, 
                label=f'训练轮次 {i+1}', color=colors[i], alpha=0.8)
    ax1.set_xlabel('训练轮次 (Epoch)', fontsize=12)
    ax1.set_ylabel('损失值 (Loss)', fontsize=12)
    ax1.set_title('损失函数收敛趋势', fontsize=14, fontweight='bold')
    ax1.legend(fontsize=11)
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')
    
    # 图2: 验证AUC变化
    ax2 = axes[0, 1]
    for i, session in enumerate(training_sessions):
        session_reset = session.reset_index(drop=True)
        ax2.plot(session_reset['epoch'], session_reset['val_auc'], 
                marker='s', linewidth=2.5, markersize=5,
                label=f'训练轮次 {i+1}', color=colors[i], alpha=0.8)
    ax2.set_xlabel('训练轮次 (Epoch)', fontsize=12)
    ax2.set_ylabel('验证集 AUC 值', fontsize=12)
    ax2.set_title('验证集AUC性能提升', fontsize=14, fontweight='bold')
    ax2.legend(fontsize=11)
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim([0.5, 1.0])
    
    # 图3: 训练效果对比（柱状图 + 双Y轴）
    ax3 = axes[1, 0]
    if len(training_sessions) >= 2:
        # 计算关键指标
        auc_metrics = {
            '初始AUC': [session['val_auc'].iloc[0] for session in training_sessions],
            '最终AUC': [session['val_auc'].iloc[-1] for session in training_sessions],
            '最佳AUC': [session['val_auc'].max() for session in training_sessions]
        }
        
        auc_improvement = [session['val_auc'].iloc[-1] - session['val_auc'].iloc[0] for session in training_sessions]
        
        x = np.arange(len(training_sessions))  # 训练阶段位置
        width = 0.25  # 柱子宽度
        
        # 左侧Y轴：AUC值
        bars1 = ax3.bar(x - width, auc_metrics['初始AUC'], width, 
                       label='初始AUC', color=colors[0], alpha=0.8)
        bars2 = ax3.bar(x, auc_metrics['最终AUC'], width, 
                       label='最终AUC', color=colors[1], alpha=0.8)
        bars3 = ax3.bar(x + width, auc_metrics['最佳AUC'], width, 
                       label='最佳AUC', color=colors[2], alpha=0.8)
        
        # 右侧Y轴：AUC提升
        ax3_twin = ax3.twinx()
        bars4 = ax3_twin.bar(x + 2*width, auc_improvement, width*0.8, 
                           label='AUC提升', color=colors[3], alpha=0.9)
        
        # 添加数值标签 - AUC值
        for bars, metric_values in zip([bars1, bars2, bars3], 
                                     [auc_metrics['初始AUC'], auc_metrics['最终AUC'], 
                                      auc_metrics['最佳AUC']]):
            for bar, value in zip(bars, metric_values):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        # 添加数值标签 - AUC提升
        for bar, value in zip(bars4, auc_improvement):
            height = bar.get_height()
            ax3_twin.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                         f'+{value:.3f}', ha='center', va='bottom', fontsize=9, 
                         fontweight='bold', color=colors[3])
        
        # 设置坐标轴
        ax3.set_xlabel('训练阶段', fontsize=12)
        ax3.set_ylabel('AUC 值', fontsize=12, color='black')
        ax3_twin.set_ylabel('AUC 提升量', fontsize=12, color=colors[3])
        ax3.set_title('训练效果详细对比', fontsize=14, fontweight='bold')
        ax3.set_xticks(x)
        ax3.set_xticklabels([f'训练阶段 {i+1}' for i in range(len(training_sessions))])
        
        # 图例
        lines1, labels1 = ax3.get_legend_handles_labels()
        lines2, labels2 = ax3_twin.get_legend_handles_labels()
        ax3.legend(lines1 + lines2, labels1 + labels2, fontsize=10, loc='upper left')
        
        ax3.grid(True, alpha=0.3)
        ax3.set_ylim([0.4, 1.0])
        ax3_twin.set_ylim([0, max(auc_improvement) * 1.3])
    
    # 图4: 学习曲线平滑对比
    ax4 = axes[1, 1]
    for i, session in enumerate(training_sessions):
        session_reset = session.reset_index(drop=True)
        # 计算平滑的移动平均
        window_size = min(5, len(session_reset))
        smooth_auc = session_reset['val_auc'].rolling(window=window_size, center=True).mean()
        
        ax4.plot(session_reset['epoch'], smooth_auc, 
                linewidth=3, label=f'AUC曲线 - 训练 {i+1}', 
                color=colors[i], alpha=0.8)
    
    ax4.set_xlabel('训练轮次 (Epoch)', fontsize=12)
    ax4.set_ylabel('平滑AUC值', fontsize=12)
    ax4.set_title('平滑学习曲线对比', fontsize=14, fontweight='bold')
    ax4.legend(fontsize=11)
    ax4.grid(True, alpha=0.3)
    ax4.set_ylim([0.5, 1.0])
    
    plt.tight_layout()
    plt.savefig('训练分析图表_中文版.png', dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    plt.close()  # 关闭图形以释放内存
    
    return training_sessions

def print_statistics(training_sessions):
    """打印中文训练统计信息"""
    print("=" * 70)
    print("                      训练统计分析报告")
    print("=" * 70)
    
    for i, session in enumerate(training_sessions):
        print(f"\n📊 训练阶段 {i+1} 详细数据：")
        print(f"  ✅ 完成轮次: {len(session)} 个epoch")
        print(f"  📈 起始损失: {session['loss'].iloc[0]:.4f}")
        print(f"  📉 最终损失: {session['loss'].iloc[-1]:.4f}")
        print(f"  🔻 损失降幅: {session['loss'].iloc[0] - session['loss'].iloc[-1]:.4f}")
        print(f"  🎯 起始AUC: {session['val_auc'].iloc[0]:.4f}")
        print(f"  🏆 最终AUC: {session['val_auc'].iloc[-1]:.4f}")
        print(f"  📊 AUC提升: {session['val_auc'].iloc[-1] - session['val_auc'].iloc[0]:.4f}")
        
        # 计算训练时间
        total_time = (session['timestamp'].iloc[-1] - session['timestamp'].iloc[0]).total_seconds()
        print(f"  ⏱️  总训练时间: {total_time:.1f} 秒")
        print(f"  ⚡ 平均每轮用时: {total_time/len(session):.2f} 秒")
        
        # 计算稳定性指标
        auc_std = session['val_auc'].std()
        print(f"  📈 AUC稳定性 (标准差): {auc_std:.4f}")
    
    if len(training_sessions) >= 2:
        print(f"\n🔍 对比分析结果：")
        auc_improvement = training_sessions[1]['val_auc'].iloc[-1] - training_sessions[0]['val_auc'].iloc[-1]
        loss_improvement = training_sessions[0]['loss'].iloc[-1] - training_sessions[1]['loss'].iloc[-1]
        print(f"  🚀 AUC性能提升 (训练2 vs 训练1): {auc_improvement:.4f}")
        print(f"  📉 损失值改善 (训练2 vs 训练1): {loss_improvement:.4f}")
        
        # 效率分析
        eff1 = (training_sessions[0]['val_auc'].iloc[-1] - training_sessions[0]['val_auc'].iloc[0]) / len(training_sessions[0])
        eff2 = (training_sessions[1]['val_auc'].iloc[-1] - training_sessions[1]['val_auc'].iloc[0]) / len(training_sessions[1])
        print(f"  ⚡ 训练效率对比 (每轮AUC提升):")
        print(f"    训练阶段1: {eff1:.6f}")
        print(f"    训练阶段2: {eff2:.6f}")

def create_summary_report(training_sessions):
    """创建中文训练总结报告"""
    report = []
    report.append("🎯 训练总结报告")
    report.append("=" * 50)
    
    for i, session in enumerate(training_sessions):
        report.append(f"\n📊 训练阶段 {i+1} 总结:")
        report.append(f"- 🏆 最佳AUC: {session['val_auc'].max():.4f}")
        report.append(f"- 📉 最低损失: {session['loss'].min():.4f}")
        report.append(f"- ⚡ 收敛速度: {'快速' if len(session) < 45 else '中等'}")
        
        # 判断训练质量
        final_auc = session['val_auc'].iloc[-1]
        if final_auc > 0.9:
            quality = "优秀 🌟"
        elif final_auc > 0.8:
            quality = "良好 👍"
        elif final_auc > 0.7:
            quality = "一般 ⭐"
        else:
            quality = "较差 ❌"
        report.append(f"- 🎯 训练质量: {quality}")
    
    # 保存报告
    with open('训练报告_中文版.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    return report

if __name__ == "__main__":
    # 解析日志文件
    df = parse_training_log('training.log')
    
    if df.empty:
        print("❌ 在training.log中未找到训练数据")
        exit(1)
    
    print("🚀 开始生成中文版训练分析图表...")
    
    # 创建可视化图表
    training_sessions = create_visualizations(df)
    
    # 打印统计信息
    print_statistics(training_sessions)
    
    # 创建总结报告
    report = create_summary_report(training_sessions)
    
    print(f"\n✅ 中文版图表已保存为 '训练分析图表_中文版.png'")
    print(f"📄 中文版总结报告已保存为 '训练报告_中文版.txt'")
    print(f"📊 总共分析了 {len(df)} 条训练记录")
    print("\n🎉 分析完成！") 